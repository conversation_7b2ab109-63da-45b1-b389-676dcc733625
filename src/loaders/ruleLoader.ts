import { readdirSync, readFileSync } from "fs";
import { join } from "path";
import YAML from "yaml";
import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';
import { loadHolidays } from './holidayLoader.js'; // Import loadHolidays

// Updated RULES structure to support practice areas
// Key format: "jurisdiction" or "jurisdiction_practiceArea"
const RULES: Record<string, any> = {};

// Practice area enum for type safety
export enum PracticeArea {
  PERSONAL_INJURY = 'personal_injury',
  CRIMINAL_DEFENSE = 'criminal_defense',
  FAMILY_LAW = 'family_law'
}

// Helper function to create rule key
function createRuleKey(jurisdiction: string, practiceArea?: string): string {
  return practiceArea ? `${jurisdiction}_${practiceArea}` : jurisdiction;
}

// Helper function to find practice-area-specific file
function findPracticeAreaFile(files: string[], jurisdiction: string, practiceArea: string): string | null {
  const expectedFileName = `${jurisdiction}_${practiceArea}.yaml`;
  return files.find(f => f === expectedFileName) || null;
}

// Updated loadRules function with practice area support
export async function loadRules(jurisdiction?: string, practiceArea?: string): Promise<void> {
  const rulesDir = join(process.cwd(), "rules");
  const files = readdirSync(rulesDir);
  const yamlFiles = files.filter(f => f.endsWith(".yaml"));

  if (jurisdiction) {
    if (practiceArea) {
      // Load specific practice area file
      const targetFile = findPracticeAreaFile(yamlFiles, jurisdiction, practiceArea);

      if (targetFile) {
        const ruleKey = createRuleKey(jurisdiction, practiceArea);
        console.log(`Loading rules for ${jurisdiction} - ${practiceArea} from ${targetFile}`);

        try {
          const content = readFileSync(join(rulesDir, targetFile), "utf8");
          const doc = YAML.parse(content);
          RULES[ruleKey] = doc;
          console.log(`Successfully loaded ${practiceArea} rules for ${jurisdiction}`);
        } catch (e) {
          console.error(`Error loading practice area rule file ${targetFile}:`, e);
        }
      } else {
        console.warn(`No practice area rule file found for ${jurisdiction}_${practiceArea}.yaml`);
      }
    } else {
      // Load general jurisdiction file (backward compatibility)
      const targetFile = yamlFiles.find(f => {
        try {
          const content = readFileSync(join(rulesDir, f), "utf8");
          const doc = YAML.parse(content);
          return doc?.meta?.jurisdiction === jurisdiction && !f.includes('_');
        } catch (e) {
          console.error(`Error parsing ${f}:`, e);
          return false;
        }
      });

      if (targetFile) {
        console.log(`Loading general rules for jurisdiction: ${jurisdiction} from ${targetFile}`);
        try {
          const content = readFileSync(join(rulesDir, targetFile), "utf8");
          const doc = YAML.parse(content);
          RULES[jurisdiction] = doc;
        } catch (e) {
          console.error(`Error loading general rule file ${targetFile}:`, e);
        }
      } else {
        console.warn(`No general rule file found for jurisdiction: ${jurisdiction}`);
      }
    }
  } else {
    // Load all files if no specific jurisdiction is given
    console.log(`Loading all rule files from ${rulesDir}...`);
    yamlFiles.forEach(f => {
      try {
        const content = readFileSync(join(rulesDir, f), "utf8");
        const doc = YAML.parse(content);

        if (doc?.meta?.jurisdiction) {
          // Determine if this is a practice-area-specific file
          const practiceAreaMatch = f.match(/^(.+)_([^_]+)\.yaml$/);

          if (practiceAreaMatch) {
            // Practice area specific file
            const [, fileJurisdiction, filePracticeArea] = practiceAreaMatch;
            const ruleKey = createRuleKey(fileJurisdiction, filePracticeArea);
            RULES[ruleKey] = doc;
            console.log(`Loaded practice area rules: ${ruleKey}`);
          } else {
            // General jurisdiction file
            RULES[doc.meta.jurisdiction] = doc;
            console.log(`Loaded general rules: ${doc.meta.jurisdiction}`);
          }
        } else {
          console.warn(`Rule file ${f} is missing meta.jurisdiction`);
        }
      } catch (e) {
        console.error(`Error parsing ${f}:`, e);
      }
    });
    console.log(`Loaded rules for ${Object.keys(RULES).length} jurisdiction/practice area combinations.`);
  }
}

// Updated getRules function with practice area support
export const getRules = (jurisdiction: string, practiceArea?: string) => {
  const ruleKey = createRuleKey(jurisdiction, practiceArea);
  return RULES[ruleKey];
};

// Helper to check if rules for a specific jurisdiction/practice area are loaded
export const areRulesLoaded = (jurisdiction: string, practiceArea?: string): boolean => {
  const ruleKey = createRuleKey(jurisdiction, practiceArea);
  return !!RULES[ruleKey];
};

// Updated getRule function with practice area support
export function getRule(jurisdiction: string, triggerCode: string, practiceArea?: string) {
  const ruleKey = createRuleKey(jurisdiction, practiceArea);
  const pack = RULES[ruleKey];

  if (!pack || !pack.triggers) {
    const contextInfo = practiceArea ? `${jurisdiction} - ${practiceArea}` : jurisdiction;
    throw new Error(`Rules not found or improperly structured for: ${contextInfo}`);
  }

  const trigger = pack.triggers.find((t: any) => t.code === triggerCode);

  if (!trigger) {
    const contextInfo = practiceArea ? `${jurisdiction} - ${practiceArea}` : jurisdiction;
    throw new Error(`Trigger code '${triggerCode}' not found for: ${contextInfo}`);
  }

  return trigger;
}

// Backward compatibility functions (deprecated but maintained for existing code)
export const getRulesLegacy = (jur: string) => RULES[jur];
export const areRulesLoadedLegacy = (jur: string): boolean => !!RULES[jur];
export function getRuleLegacy(jur: string, triggerCode: string) {
  return getRule(jur, triggerCode);
}

// Helper function to get available practice areas for a jurisdiction
export function getAvailablePracticeAreas(jurisdiction: string): string[] {
  const practiceAreas: string[] = [];

  Object.keys(RULES).forEach(key => {
    if (key.startsWith(`${jurisdiction}_`)) {
      const practiceArea = key.substring(jurisdiction.length + 1);
      practiceAreas.push(practiceArea);
    }
  });

  return practiceAreas;
}

// Helper function to list all loaded jurisdictions and practice areas
export function getLoadedRules(): { jurisdiction: string; practiceArea?: string }[] {
  return Object.keys(RULES).map(key => {
    const parts = key.split('_');
    if (parts.length === 2) {
      return { jurisdiction: parts[0], practiceArea: parts[1] };
    } else {
      return { jurisdiction: key };
    }
  });
}

// Helper function to load all practice areas for a jurisdiction
export async function loadAllPracticeAreas(jurisdiction: string): Promise<void> {
  const practiceAreas = Object.values(PracticeArea);

  for (const practiceArea of practiceAreas) {
    try {
      await loadRules(jurisdiction, practiceArea);
    } catch (error) {
      console.log(`Practice area ${practiceArea} not available for ${jurisdiction}`);
    }
  }
}

// Ensure rules are loaded at startup (load all available rules)
loadRules().catch(err => console.error('Failed to load rules:', err));

// Preload holidays for the current and next year on startup
const currentYear = new Date().getFullYear();
loadHolidays(currentYear)
  .then(() => console.log(`Holiday cache preloaded for ${currentYear}`))
  .catch(err => console.error(`Failed to preload holidays for ${currentYear}:`, err));
loadHolidays(currentYear + 1)
  .then(() => console.log(`Holiday cache preloaded for ${currentYear + 1}`))
  .catch(err => console.error(`Failed to preload holidays for ${currentYear + 1}:`, err));
